import 'package:budapp/features/common/models/time_period.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/features/common/services/time_period_service.dart';
import 'package:budapp/features/common/widgets/horizontal_period_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes for comprehensive testing
class MockTimePeriodNotifier extends TimePeriodNotifier {
  MockTimePeriodNotifier(this._period);

  final TimePeriod _period;

  @override
  TimePeriod build() => _period;

  @override
  Future<void> selectPeriod(TimePeriod period) async {
    // Mock implementation - can be overridden in tests
    state = period;
  }
}

class MockMethodChannel extends Mock implements MethodChannel {}

// Test notifier for controlled state
class TestTimePeriodNotifier extends TimePeriodNotifier {
  TestTimePeriodNotifier(this._initialPeriod);

  final TimePeriod _initialPeriod;

  @override
  TimePeriod build() => _initialPeriod;

  @override
  Future<void> selectPeriod(TimePeriod period) async {
    state = period;
  }
}

// Test notifier with tracking capabilities
class TestTrackingTimePeriodNotifier extends TimePeriodNotifier {
  TestTrackingTimePeriodNotifier(
    this._initialPeriod, {
    required this.onSelectPeriod,
  });

  final TimePeriod _initialPeriod;
  final void Function(TimePeriod) onSelectPeriod;

  @override
  TimePeriod build() => _initialPeriod;

  @override
  Future<void> selectPeriod(TimePeriod period) async {
    onSelectPeriod(period);
    state = period;
  }
}

// Test callback tracker
class CallbackTracker {
  int callCount = 0;
  void call() => callCount++;
}

void main() {
  setUpAll(() {
    // Register fallback values for Mocktail
    registerFallbackValue(
      TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2025, 1, 1),
        endDate: DateTime(2025, 1, 31),
        displayName: 'January 2025',
        dateRangeText: '01 Jan - 31 Jan',
        year: 2025,
        month: 1,
      ),
    );
  });

  group('HorizontalPeriodSelector', () {
    testWidgets('should render without errors', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
    });

    testWidgets('should display compact period formats', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should find period text in compact format (e.g., "Jan 25", "Feb 25", etc.)
      final currentMonth = TimePeriodService.getCurrentMonth();
      final compactText = TimePeriodService.formatPeriodCompact(currentMonth);

      expect(find.text(compactText), findsOneWidget);
    });

    testWidgets('should handle period selection', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find a period that's not the current one and tap it
      final pageView = find.byType(PageView);
      expect(pageView, findsOneWidget);

      // The widget should be scrollable
      await tester.drag(pageView, const Offset(-100, 0));
      await tester.pumpAndSettle();

      // This basic test ensures the widget renders and is interactive
    });

    testWidgets('should respect minimum height for accessibility', (
      tester,
    ) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(height: 44),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      final sizedBox = tester.widget<SizedBox>(
        find
            .descendant(
              of: find.byType(HorizontalPeriodSelector),
              matching: find.byType(SizedBox),
            )
            .first,
      );

      expect(sizedBox.height, equals(44.0));
    });

    testWidgets('should have proper semantics for accessibility', (
      tester,
    ) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Check that the widget has proper semantics
      expect(
        find.bySemanticsLabel('Time period selector'),
        findsOneWidget,
      );
    });

    test('should format periods correctly using service', () {
      final testPeriod = TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2025, 1, 1),
        endDate: DateTime(2025, 1, 31),
        displayName: 'January 2025',
        dateRangeText: '01 Jan - 31 Jan',
        year: 2025,
        month: 1,
      );

      final formatted = TimePeriodService.formatPeriodCompact(testPeriod);
      expect(formatted, equals('Jan 25'));
    });

    group('State Synchronization', () {
      testWidgets('synchronizes with external period changes', (tester) async {
        final initialPeriod = TimePeriod(
          type: PeriodType.monthly,
          startDate: DateTime(2025, 1, 1),
          endDate: DateTime(2025, 1, 31),
          displayName: 'January 2025',
          dateRangeText: '01 Jan - 31 Jan',
          year: 2025,
          month: 1,
        );

        final testNotifier = TestTimePeriodNotifier(initialPeriod);

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              timePeriodNotifierProvider.overrideWith(() => testNotifier),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Change period externally
        final newPeriod = TimePeriod(
          type: PeriodType.monthly,
          startDate: DateTime(2025, 3, 1),
          endDate: DateTime(2025, 3, 31),
          displayName: 'March 2025',
          dateRangeText: '01 Mar - 31 Mar',
          year: 2025,
          month: 3,
        );

        await testNotifier.selectPeriod(newPeriod);
        await tester.pumpAndSettle();

        // Verify the selector updated to show the new period
        final compactText = TimePeriodService.formatPeriodCompact(newPeriod);
        expect(find.text(compactText), findsOneWidget);
      });

      testWidgets('prevents infinite loops during synchronization', (
        tester,
      ) async {
        final initialPeriod = TimePeriodService.getCurrentMonth();

        var selectPeriodCallCount = 0;

        // Override selectPeriod to count calls
        final trackedNotifier = TestTrackingTimePeriodNotifier(
          initialPeriod,
          onSelectPeriod: (period) => selectPeriodCallCount++,
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              timePeriodNotifierProvider.overrideWith(() => trackedNotifier),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Multiple external updates shouldn't cause infinite loops
        await trackedNotifier.selectPeriod(initialPeriod);
        await tester.pump();
        await trackedNotifier.selectPeriod(initialPeriod);
        await tester.pump();
        await trackedNotifier.selectPeriod(initialPeriod);
        await tester.pumpAndSettle();

        // Should handle gracefully without excessive calls
        expect(selectPeriodCallCount, lessThan(10));
      });

      testWidgets('handles rapid period changes gracefully', (tester) async {
        final testNotifier = TestTimePeriodNotifier(
          TimePeriodService.getCurrentMonth(),
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              timePeriodNotifierProvider.overrideWith(() => testNotifier),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Simulate rapid period changes
        final periods = TimePeriodService.getHorizontalPeriods();
        for (var i = 0; i < 5 && i < periods.length; i++) {
          await testNotifier.selectPeriod(periods[i]);
          await tester.pump(const Duration(milliseconds: 50));
        }

        // Allow time for state changes to process without waiting for all animations to settle
        await tester.pump(const Duration(milliseconds: 100));
        await tester.pump(const Duration(milliseconds: 100));

        // Should handle without crashes
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
      });
    });

    group('User Interaction', () {
      testWidgets('responds to tap gestures', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: SizedBox(
                  width: 400,
                  height: 100,
                  child: HorizontalPeriodSelector(),
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find the PageView which contains the period items
        final pageView = find.byType(PageView);
        expect(pageView, findsOneWidget);

        // Verify the widget is interactive by performing a drag gesture
        await tester.drag(pageView, const Offset(-200, 0));

        // Allow time for the drag to process
        await tester.pump(const Duration(milliseconds: 100));
        await tester.pump(const Duration(milliseconds: 200));

        // The widget should still be present and functional after interaction
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
        expect(find.byType(PageView), findsOneWidget);
      });

      testWidgets('handles swipe gestures for navigation', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        // Allow widget to initialize
        await tester.pump();
        await tester.pump(const Duration(milliseconds: 100));

        final pageView = find.byType(PageView);
        expect(pageView, findsOneWidget);

        // Test left swipe (next period) - use warnIfMissed: false to suppress warnings
        await tester.drag(pageView, const Offset(-200, 0), warnIfMissed: false);
        // Allow animation to start and progress
        await tester.pump();
        await tester.pump(const Duration(milliseconds: 100));
        await tester.pump(const Duration(milliseconds: 200));
        await tester.pump(const Duration(milliseconds: 100));

        // Test right swipe (previous period)
        await tester.drag(pageView, const Offset(200, 0), warnIfMissed: false);
        // Allow animation to start and progress
        await tester.pump();
        await tester.pump(const Duration(milliseconds: 100));
        await tester.pump(const Duration(milliseconds: 200));
        await tester.pump(const Duration(milliseconds: 100));

        // Should handle swipes without errors
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
      });

      testWidgets('provides smooth scroll animation', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find the PageView to trigger scroll animation
        final pageView = find.byType(PageView);
        expect(pageView, findsOneWidget);

        // Drag to trigger smooth scroll animation
        await tester.drag(pageView, const Offset(-150, 0));

        // Verify animation is in progress
        await tester.pump(const Duration(milliseconds: 100));
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);

        // Allow animation to process without waiting for complete settle
        await tester.pump(const Duration(milliseconds: 200));
        await tester.pump(const Duration(milliseconds: 100));
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
      });
    });

    group('Future Navigation Support', () {
      testWidgets('respects allowFutureNavigation flag', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(
                  allowFutureNavigation: true,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        final widget = tester.widget<HorizontalPeriodSelector>(
          find.byType(HorizontalPeriodSelector),
        );
        expect(widget.allowFutureNavigation, isTrue);
      });

      testWidgets('limits future navigation when disabled', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(
                  allowFutureNavigation: false,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        final widget = tester.widget<HorizontalPeriodSelector>(
          find.byType(HorizontalPeriodSelector),
        );
        expect(widget.allowFutureNavigation, isFalse);

        // Widget should still render periods correctly
        expect(find.byType(PageView), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('provides comprehensive semantic labels', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Main selector semantics
        expect(
          find.bySemanticsLabel('Time period selector'),
          findsOneWidget,
        );

        // Period item semantics
        final semantics = tester.getSemantics(
          find.byType(HorizontalPeriodSelector),
        );

        // Should have semantic information for accessibility
        expect(semantics, isNotNull);
      });

      testWidgets('supports screen reader navigation', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find semantic widgets for period items
        final semanticWidgets = find.descendant(
          of: find.byType(HorizontalPeriodSelector),
          matching: find.byType(Semantics),
        );

        expect(semanticWidgets, findsAtLeastNWidgets(1));

        // Each selectable period item should have button semantics
        var foundSelectablePeriod = false;
        for (final widget in semanticWidgets.evaluate()) {
          final semanticsWidget = widget.widget as Semantics;
          if (semanticsWidget.properties.label?.contains('period') ?? false) {
            // Only check button property if it's explicitly set (not null)
            if (semanticsWidget.properties.button != null) {
              // If button property is set, it should be true for selectable periods
              expect(semanticsWidget.properties.button, isTrue);
              foundSelectablePeriod = true;
            }
          }
        }

        // Ensure we found at least one selectable period with button semantics
        expect(
          foundSelectablePeriod,
          isTrue,
          reason:
              'Should find at least one selectable period with button semantics',
        );
      });

      testWidgets('maintains minimum tap target size', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(height: 44),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Height should meet accessibility guidelines
        final sizedBox = tester.widget<SizedBox>(
          find.byType(SizedBox).first,
        );
        expect(sizedBox.height, greaterThanOrEqualTo(44.0));
      });
    });

    group('Performance', () {
      testWidgets('handles large period lists efficiently', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        // Initial render should be fast
        await tester.pumpAndSettle();
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);

        // Test that the widget renders efficiently with many periods
        // by checking that it doesn't take too long to build
        final stopwatch = Stopwatch()..start();

        // Trigger a rebuild by pumping
        await tester.pump();

        stopwatch.stop();

        // Should render quickly (less than 100ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
      });

      testWidgets('lazy loads periods correctly', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // PageView should be used for efficient rendering
        expect(find.byType(PageView), findsOneWidget);

        // Should contain multiple period items for performance test
        expect(find.byType(Container), findsAtLeastNWidgets(1));
      });

      testWidgets('disposes resources properly', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);

        // Remove widget
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: SizedBox(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Widget should be removed without errors
        expect(find.byType(HorizontalPeriodSelector), findsNothing);
      });
    });

    group('Visual States', () {
      testWidgets('highlights selected period correctly', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should have at least one container with selection styling
        final containers = find.byType(Container);
        expect(containers, findsAtLeastNWidgets(1));
      });

      testWidgets('shows current month indicator', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Current month should be highlighted differently
        final currentMonth = TimePeriodService.getCurrentMonth();
        final compactText = TimePeriodService.formatPeriodCompact(currentMonth);

        // Should find current month text
        expect(find.text(compactText), findsOneWidget);
      });

      testWidgets('animates between states smoothly', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find animated containers
        final animatedContainers = find.byType(AnimatedContainer);
        expect(animatedContainers, findsAtLeastNWidgets(1));

        // Verify animation duration
        final animatedContainer = tester.widget<AnimatedContainer>(
          animatedContainers.first,
        );
        expect(
          animatedContainer.duration,
          lessThanOrEqualTo(const Duration(milliseconds: 300)),
        );
        expect(animatedContainer.curve, isA<Curve>());
      });
    });

    group('Error Handling', () {
      testWidgets('handles period selection errors gracefully', (tester) async {
        // Use a test notifier that can simulate errors
        final testNotifier = TestTimePeriodNotifier(
          TimePeriodService.getCurrentMonth(),
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              timePeriodNotifierProvider.overrideWith(() => testNotifier),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Widget should render without errors
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);

        // Try to interact with widget - should not crash
        final pageView = find.byType(PageView);
        if (pageView.evaluate().isNotEmpty) {
          await tester.drag(
            pageView,
            const Offset(-50, 0),
            warnIfMissed: false,
          );
          await tester.pump(const Duration(milliseconds: 100));
        }

        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
      });

      testWidgets('handles initialization edge cases', (tester) async {
        // Test with a provider that has minimal setup
        final testNotifier = TestTimePeriodNotifier(
          TimePeriodService.getCurrentMonth(),
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              timePeriodNotifierProvider.overrideWith(() => testNotifier),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Widget should render properly with minimal setup
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
      });
    });

    group('Tap Selection Enhancement', () {
      testWidgets('tapping a period should actually select it', (tester) async {
        var selectedPeriodCallCount = 0;
        TimePeriod? lastSelectedPeriod;

        final testNotifier = TestTrackingTimePeriodNotifier(
          TimePeriodService.getCurrentMonth(),
          onSelectPeriod: (period) {
            selectedPeriodCallCount++;
            lastSelectedPeriod = period;
          },
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              timePeriodNotifierProvider.overrideWith(() => testNotifier),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: SizedBox(
                  width: 400,
                  height: 100,
                  child: HorizontalPeriodSelector(),
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find a tappable period item (not the current center one)
        final gestureDetectors = find.byType(GestureDetector);
        expect(gestureDetectors, findsAtLeastNWidgets(3));

        // Reset counter
        selectedPeriodCallCount = 0;
        lastSelectedPeriod = null;

        // Tap on a period item
        await tester.tap(gestureDetectors.at(1)); // Tap the second visible period
        await tester.pumpAndSettle();

        // Verify that period selection was called
        expect(selectedPeriodCallCount, greaterThan(0));
        expect(lastSelectedPeriod, isNotNull);
      });

      testWidgets('tap selection should trigger haptic feedback', (tester) async {
        const channel = MethodChannel('flutter/hapticfeedback');
        final methodCalls = <MethodCall>[];

        tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
          channel,
          (MethodCall methodCall) async {
            methodCalls.add(methodCall);
          },
        );

        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: SizedBox(
                  width: 400,
                  height: 100,
                  child: HorizontalPeriodSelector(),
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        final gestureDetectors = find.byType(GestureDetector);
        await tester.tap(gestureDetectors.at(1));
        await tester.pumpAndSettle();

        // Verify haptic feedback was called
        expect(
          methodCalls,
          contains(isA<MethodCall>().having(
            (call) => call.method,
            'method',
            'HapticFeedback.lightImpact',
          )),
        );

        // Clean up
        tester.binding.defaultBinaryMessenger
            .setMockMethodCallHandler(channel, null);
      });
    });

    group('Infinite Scrolling', () {
      testWidgets('should support extensive scrolling without errors', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        final pageView = find.byType(PageView);
        expect(pageView, findsOneWidget);

        // Simulate moderate scrolling in both directions
        await tester.drag(pageView, const Offset(-200, 0), warnIfMissed: false);
        await tester.pump(const Duration(milliseconds: 100));
        
        await tester.drag(pageView, const Offset(400, 0), warnIfMissed: false);
        await tester.pump(const Duration(milliseconds: 100));

        // Should still render without errors after scrolling
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
        expect(find.byType(PageView), findsOneWidget);
      });

      testWidgets('should display period text at different scroll positions', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should display some text widgets with period information
        expect(find.byType(Text), findsAtLeastNWidgets(3));
        
        final pageView = find.byType(PageView);
        
        // Small scroll to verify periods are still displayed
        await tester.drag(pageView, const Offset(-100, 0), warnIfMissed: false);
        await tester.pump(const Duration(milliseconds: 200));

        // Should still display period text after scrolling
        expect(find.byType(Text), findsAtLeastNWidgets(1));
      });
    });

    group('Center-Based Selection', () {
      testWidgets('center period should always be considered selected', (tester) async {
        final testNotifier = TestTimePeriodNotifier(
          TimePeriodService.getCurrentMonth(),
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              timePeriodNotifierProvider.overrideWith(() => testNotifier),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // After scrolling, the center period should be highlighted as selected
        final pageView = find.byType(PageView);
        await tester.drag(pageView, const Offset(-200, 0));
        await tester.pump(const Duration(milliseconds: 200));
        await tester.pump(const Duration(milliseconds: 200));

        // Should still have selected visual styling
        final containers = find.byType(Container);
        expect(containers, findsAtLeastNWidgets(1));
      });
    });

    group('Calendar Period Selection Alignment', () {
      testWidgets('calendar selection of Aug 2023 should display Aug 2023', (tester) async {
        var lastSelectedPeriod = TimePeriodService.getCurrentMonth();

        final testNotifier = TestTrackingTimePeriodNotifier(
          lastSelectedPeriod,
          onSelectPeriod: (period) {
            lastSelectedPeriod = period;
          },
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              timePeriodNotifierProvider.overrideWith(() => testNotifier),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Simulate calendar selection of August 2023
        final targetPeriod = TimePeriodService.getMonthPeriod(2023, 8);
        await testNotifier.selectPeriod(targetPeriod);
        
        // Force widget rebuild and wait for synchronization
        await tester.pump();
        await tester.pump(const Duration(milliseconds: 350)); // Wait for animation

        // Debug: Print all text widgets to see what's displayed
        final allTexts = tester.widgetList<Text>(find.byType(Text));
        print('Debug - All text widgets: ${allTexts.map((t) => t.data).toList()}');
        print('Debug - Target period: ${targetPeriod.displayName}');
        print('Debug - Last selected period: ${lastSelectedPeriod.displayName}');
        
        // Verify the displayed period is August 2023 (check both formatted versions)
        final foundAug23 = find.text('Aug 23').evaluate().isNotEmpty;
        if (!foundAug23) {
          // Try different possible formats
          final possibleTexts = ['Aug 2023', 'August 2023', 'Aug 23', '2023-08'];
          for (final text in possibleTexts) {
            if (find.text(text).evaluate().isNotEmpty) {
              print('Debug - Found alternative text: $text');
              break;
            }
          }
        }
        expect(find.text('Aug 23'), findsOneWidget);
      });

      testWidgets('calendar selection of Aug 2025 should display Aug 2025', (tester) async {
        var lastSelectedPeriod = TimePeriodService.getMonthPeriod(2023, 8); // Start with Aug 2023

        final testNotifier = TestTrackingTimePeriodNotifier(
          lastSelectedPeriod,
          onSelectPeriod: (period) {
            lastSelectedPeriod = period;
          },
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              timePeriodNotifierProvider.overrideWith(() => testNotifier),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Simulate calendar selection of August 2025
        final targetPeriod = TimePeriodService.getMonthPeriod(2025, 8);
        await testNotifier.selectPeriod(targetPeriod);
        await tester.pump();
        await tester.pump(const Duration(milliseconds: 100));

        // Verify the displayed period is August 2025 (not May 2024 as reported)
        expect(find.text('Aug 25'), findsOneWidget);
      });

      testWidgets('multiple calendar selections should work correctly', (tester) async {
        var lastSelectedPeriod = TimePeriodService.getCurrentMonth();

        final testNotifier = TestTrackingTimePeriodNotifier(
          lastSelectedPeriod,
          onSelectPeriod: (period) {
            lastSelectedPeriod = period;
          },
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              timePeriodNotifierProvider.overrideWith(() => testNotifier),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Test multiple calendar selections
        final testPeriods = [
          (TimePeriodService.getMonthPeriod(2023, 1), 'Jan 23'),
          (TimePeriodService.getMonthPeriod(2023, 8), 'Aug 23'),
          (TimePeriodService.getMonthPeriod(2025, 8), 'Aug 25'),
          (TimePeriodService.getMonthPeriod(2024, 12), 'Dec 24'),
        ];

        for (final (testPeriod, expectedText) in testPeriods) {
          // Simulate calendar selection
          await testNotifier.selectPeriod(testPeriod);
          await tester.pump();
          await tester.pump(const Duration(milliseconds: 100));

          // Verify the correct period is displayed
          expect(find.text(expectedText), findsOneWidget, 
            reason: 'Calendar selection failed for ${testPeriod.displayName}');
        }
      });
    });
  });
}
